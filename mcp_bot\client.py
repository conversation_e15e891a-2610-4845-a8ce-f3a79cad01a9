import asyncio
import os
import sys
import uuid
from contextlib import AsyncExitStack
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

from dotenv import load_dotenv

# --- MCP core (Python SDK) ---
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# --- LangChain / LangGraph stack ---
from langchain_openai import ChatOpenAI
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from langchain.schema import BaseMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from DB.db_ops import insert_chat_history, fetch_chat_history, delete_chat_history
from langchain.callbacks.base import BaseCallbackHandler



# ----------------------------
# Logging setup
# ----------------------------
from helper.logger_setup import setup_logger
logger = setup_logger('client', 'client.log')

# ----------------------------
# Config & Environment
# ----------------------------
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables.")

DEFAULT_MODEL = "gpt-4o"



# GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# if not GEMINI_API_KEY:
#     raise ValueError("GEMINI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = "gemini-2.0-flash-exp"



class ToolLoggingCallback(BaseCallbackHandler):
    def on_tool_start(self, serialized, input_str, **kwargs):
        tool_name = serialized.get("name", "unknown_tool")
        print(f"\n🛠️ LLM called tool: {tool_name}")
        print(f"   🔹 Input: {input_str}")

    def on_tool_end(self, output, **kwargs):
        print(f"   🔸 Output: {output}\n")


@dataclass
class MCPServerSpec:
    """Defines an MCP server for stdio connection."""
    path: str
    args: Optional[List[str]] = None

    @property
    def command(self) -> str:
        return "python" if self.path.endswith(".py") else "node"

    @property
    def full_args(self) -> List[str]:
        return [self.path] + (self.args or [])


class MCPMultiServerClient:
    def __init__(self, servers: List[MCPServerSpec], thread_id: Optional[str] = None):
        self.servers = servers
        self.exit_stack = AsyncExitStack()
        self.sessions: List[ClientSession] = []
        self.tools = []
        self.app = None

        # Setup thread_id
        if thread_id:
            self.thread_id = thread_id
        else:
            self.thread_id = str(uuid.uuid4())

        # Load last 7 chat history messages from DB
        self.thread_history = fetch_chat_history(self.thread_id, last_n=7)


    async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
        try:
            params = StdioServerParameters(command=spec.command, args=spec.full_args)
            read, write = await self.exit_stack.enter_async_context(stdio_client(params))
            session = await self.exit_stack.enter_async_context(ClientSession(read, write))
            await session.initialize()
            return session
        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
            raise

    async def connect(self):
        try:
            for spec in self.servers:
                session = await self._connect_one(spec)
                self.sessions.append(session)

            loaded_tools = []
            for s in self.sessions:
                maybe = load_mcp_tools(s)
                ts = await maybe if asyncio.iscoroutine(maybe) else maybe
                if ts:
                    loaded_tools.extend(ts)

            if not loaded_tools:
                logger.error("[TOOLS] No MCP tools found in any session")
                raise RuntimeError("No MCP tools found")

            self.tools = loaded_tools

            # Instantiate LLM
            llm = ChatOpenAI(model=DEFAULT_MODEL, 
                             temperature=0, 
                             stream_usage=True,
                             callbacks=[ToolLoggingCallback()])

            # llm = ChatGoogleGenerativeAI(
            #         model=DEFAULT_MODEL,
            #         temperature=0,
            #         google_api_key=GEMINI_API_KEY,
            #         callbacks=[ToolLoggingCallback()]
            #     )

            # Attach callback to tools as well
            for tool in loaded_tools:
                tool.callbacks = [ToolLoggingCallback()]

            tool_descriptions = []
            for t in loaded_tools:
                desc = getattr(t, "description", "") or "No description provided"
                tool_descriptions.append(f"- **{t.name}**: {desc}")

            tools_block = "\n".join(tool_descriptions)
            now = datetime.now()
            # Agent prompt
            system_prompt = f"""
You are an **Energy Data Analysis Agent** at **Integrum Energy Infrastructure Ltd.**, 
powered by MCP-provided tools.

This session is linked to a WhatsApp customer with mobile number: **{self.thread_id}**.  
- This number uniquely identifies the customer.  
- Always use it implicitly for plant-specific lookups.  
- **Never expose or repeat the mobile number** — instead, refer to the customer by their registered contact name.
Today's date is {now.strftime('%Y-%m-%d')}
The following tools are available (discovered from the MCP servers at runtime):

{tools_block}

---
always call this tool first get_plants_by_mobile_number to fetch the data then only use other tools. when ever user query comes
carefully analyze and understand the user’s query to determine the true intent. 
when ever user query comes call the tool get_plants_by_mobile_number and identify this is a hybrid plant or not based on the output from the tool need to call the hybrid functions properly
Then, call the most relevant tool(s) as needed and generate a clear, accurate response based on the results.

### Your Objectives:
1. **Clarify intent first** – ask follow-up questions when necessary.  
2. **Prioritize tool usage** – always rely on MCP tools when available.  
3. **Communicate professionally** – provide concise, structured responses (bullet points, tables, or JSON if suitable).  
4. **Explain reasoning briefly** before making tool calls.  
5. **Tool failures** – acknowledge the issue (e.g., “I wasn’t able to fetch that data just now. Would you like me to retry or check another metric?”).  
6. **Respect confidentiality** – never reveal internal identifiers (e.g., `plant_id`, mobile numbers). Use only `plant_long_name` in customer-facing answers.  
7. **Temporal limitation** – you cannot provide "today’s" values (e.g., today’s generation). You may only report **yesterday or earlier**.  
8. **Multi-plant handling** –  
   - Use `get_plants_by_mobile_number` to check if this customer has more than one plant.  
   - If a query (e.g., "What was yesterday’s generation?") applies to all plants, first provide the **total combined generation**.  
   - Then politely ask: *“Would you like me to show the breakdown by each plant as well?”*  
   - Only show the breakdown if the customer requests it.  
   - ⚡ **Important:** The same WhatsApp number may be linked to multiple plants. Each plant has a unique `plant_id`, which must be respected for accurate tool lookups. 
9. **Tone** – maintain a polite, professional style. Default to short answers unless detail is requested.  
10. **Greet the customer by `contact_person`** if available; otherwise, keep greeting generic.  
---
"""

            self.app = create_react_agent(
                model=llm,
                tools=self.tools,
                checkpointer=None,
                prompt=system_prompt,
            )
        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
            raise

    async def ainvoke(self, user_text: str) -> str:
        if self.app is None:
            logger.error("[INVOKE] Client not connected, app is None")
            raise RuntimeError("Client not connected. Call connect() first.")

        # Use in-memory thread_history (always loaded from DB on __init__ and after reset)
        messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in self.thread_history]
        messages_for_state.append(("user", user_text))
        state = {"messages": messages_for_state}
        config = {"configurable": {"thread_id": self.thread_id}}

        try:
            result = await self.app.ainvoke(state, config=config)
            print(f"result: {result}")
        except Exception as e:
            logger.error(f"[INVOKE] Agent invocation failed: {e}")
            raise

        # Normalize result
        content = "(no output)"
        if isinstance(result, dict):
            if "messages" in result and isinstance(result["messages"], list):
                last = result["messages"][-1]
                if isinstance(last, BaseMessage):
                    content = last.content
                elif isinstance(last, tuple) and len(last) >= 2:
                    content = last[1]
                elif isinstance(last, dict):
                    content = last.get("content") or last.get("text")
                else:
                    content = str(last)
            elif "output" in result:
                content = str(result["output"])
            elif "text" in result:
                content = str(result["text"])
        elif isinstance(result, str):
            content = result

        # Persist history to DB
        now = datetime.now().astimezone().isoformat()
        new_entries = [
            {"thread_id": self.thread_id, "role": "user", "content": user_text, "timestamp": now},
            {"thread_id": self.thread_id, "role": "assistant", "content": content, "timestamp": now},
        ]
        insert_chat_history(new_entries)
        self.thread_history.extend(new_entries)

        return content

    async def reset_history(self):
        delete_chat_history(self.thread_id)
        self.thread_history = []

    async def close(self):
        await self.exit_stack.aclose()

    def _format_timestamp(self, iso_ts: Optional[str]) -> str:
        if not iso_ts:
            return ""
        try:
            dt = datetime.fromisoformat(iso_ts)
            return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
        except Exception as e:
            logger.error(f"[HISTORY] Failed to format timestamp: {e}")
            return iso_ts

    def format_history(self, last_n: Optional[int] = None) -> str:
        # Always fetch latest from DB, default to last 7
        n = last_n if last_n is not None else 7
        hist = fetch_chat_history(self.thread_id, last_n=n)
        lines = []
        for i, entry in enumerate(hist, start=1):
            role = entry.get("role", "assistant").upper()
            ts = self._format_timestamp(entry.get("timestamp", ""))
            header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
            body = entry.get("content", "")
            lines.append(header)
            lines.append(body)
            lines.append("-" * 80)
        return "\n".join(lines) if lines else "(no history)"
    






# import asyncio
# import os
# import sys
# import uuid
# import json
# from contextlib import AsyncExitStack
# from dataclasses import dataclass
# from typing import List, Optional
# from datetime import datetime

# from dotenv import load_dotenv

# # --- MCP core (Python SDK) ---
# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client

# # --- LangChain / LangGraph stack ---
# from langchain_openai import ChatOpenAI
# from langchain_mcp_adapters.tools import load_mcp_tools
# from langgraph.prebuilt import create_react_agent
# from langchain.schema import BaseMessage



# # ----------------------------
# # Logging setup
# # ----------------------------
# from helper.logger_setup import setup_logger
# logger = setup_logger('client', 'client.log')

# # ----------------------------
# # Config & Environment
# # ----------------------------
# load_dotenv()
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# if not OPENAI_API_KEY:
#     raise ValueError("OPENAI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = "gpt-4o"
# DEFAULT_MEMORY_FILE =  "mcp_bot/mcp_client_memory.json"

# from langchain.callbacks.base import BaseCallbackHandler

# class ToolLoggingCallback(BaseCallbackHandler):
#     def on_tool_start(self, serialized, input_str, **kwargs):
#         tool_name = serialized.get("name", "unknown_tool")
#         print(f"\n🛠️ LLM called tool: {tool_name}")
#         print(f"   🔹 Input: {input_str}")

#     def on_tool_end(self, output, **kwargs):
#         print(f"   🔸 Output: {output}\n")


# @dataclass
# class MCPServerSpec:
#     """Defines an MCP server for stdio connection."""
#     path: str
#     args: Optional[List[str]] = None

#     @property
#     def command(self) -> str:
#         return "python" if self.path.endswith(".py") else "node"

#     @property
#     def full_args(self) -> List[str]:
#         return [self.path] + (self.args or [])


# class MCPMultiServerClient:
#     def __init__(self, servers: List[MCPServerSpec], thread_id: Optional[str] = None):
#         # Only log errors and critical lifecycle events
#         self.servers = servers
#         self.exit_stack = AsyncExitStack()
#         self.sessions: List[ClientSession] = []
#         self.tools = []
#         self.app = None

#         # Setup memory file
#         self.memory_file = DEFAULT_MEMORY_FILE
#         os.makedirs(os.path.dirname(self.memory_file) or ".", exist_ok=True)

#         if os.path.exists(self.memory_file):
#             try:
#                 with open(self.memory_file, "r", encoding="utf-8") as f:
#                     self.memory = json.load(f)
#             except Exception as e:
#                 logger.error(f"[MEMORY] Failed to load memory file: {e}")
#                 self.memory = {}
#         else:
#             self.memory = {}

#         # ✅ Use thread_id from constructor OR memory OR generate new
#         if thread_id:
#             self.thread_id = thread_id
#         else:
#             self.thread_id = self.memory.get("thread_id", str(uuid.uuid4()))

#         self.memory.setdefault(self.thread_id, [])
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()


#     def save_memory(self):
#         with open(self.memory_file, "w", encoding="utf-8") as f:
#             json.dump(self.memory, f, indent=2)

#     async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
#         try:
#             params = StdioServerParameters(command=spec.command, args=spec.full_args)
#             read, write = await self.exit_stack.enter_async_context(stdio_client(params))
#             session = await self.exit_stack.enter_async_context(ClientSession(read, write))
#             await session.initialize()
#             return session
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
#             raise

#     async def connect(self):
#         try:
#             for spec in self.servers:
#                 session = await self._connect_one(spec)
#                 self.sessions.append(session)

#             loaded_tools = []
#             for s in self.sessions:
#                 maybe = load_mcp_tools(s)
#                 ts = await maybe if asyncio.iscoroutine(maybe) else maybe
#                 if ts:
#                     loaded_tools.extend(ts)

#             if not loaded_tools:
#                 logger.error("[TOOLS] No MCP tools found in any session")
#                 raise RuntimeError("No MCP tools found")

#             self.tools = loaded_tools

#             # Instantiate LLM
#             llm = ChatOpenAI(model=DEFAULT_MODEL, temperature=0, callbacks=[ToolLoggingCallback()])

#             # Attach callback to tools as well
#             for tool in loaded_tools:
#                 tool.callbacks = [ToolLoggingCallback()]

#             tool_descriptions = []
#             for t in loaded_tools:
#                 desc = getattr(t, "description", "") or "No description provided"
#                 tool_descriptions.append(f"- **{t.name}**: {desc}")

#             tools_block = "\n".join(tool_descriptions)
#             now = datetime.now()
#             # Agent prompt
#             system_prompt = f"""
# You are an **Energy Data Analysis Agent** at **Integrum Energy Infrastructure Ltd.**, 
# powered by MCP-provided tools.

# 📱 This session is linked to a WhatsApp customer with mobile number: **{self.thread_id}**.  
# - This number uniquely identifies the customer.  
# - Always use it implicitly for plant-specific lookups.  
# - **Never expose or repeat the mobile number** — instead, refer to the customer by their registered contact name.



# Today's date is {now.strftime('%Y-%m-%d')}


# The following tools are available (discovered from the MCP servers at runtime):

# {tools_block}

# ---
# always call this tool first get_plants_by_mobile_number to fetch the data then only use other tools. when ever user query comes
# carefully analyze and understand the user’s query to determine the true intent. 
# Then, call the most relevant tool(s) as needed and generate a clear, accurate response based on the results.

# ### 🎯 Your Objectives:
# 1. **Clarify intent first** – ask follow-up questions when necessary.  
# 2. **Prioritize tool usage** – always rely on MCP tools when available.  
# 3. **Communicate professionally** – provide concise, structured responses (bullet points, tables, or JSON if suitable).  
# 4. **Explain reasoning briefly** before making tool calls.  
# 5. **Tool failures** – acknowledge the issue (e.g., “I wasn’t able to fetch that data just now. Would you like me to retry or check another metric?”).  
# 6. **Respect confidentiality** – never reveal internal identifiers (e.g., `plant_id`, mobile numbers). Use only `plant_long_name` in customer-facing answers.  
# 7. **Temporal limitation** – you cannot provide "today’s" values (e.g., today’s generation). You may only report **yesterday or earlier**.  
# 8. **Multi-plant handling** –  
#    - Use `get_plants_by_mobile_number` to check if this customer has more than one plant.  
#    - If a query (e.g., "What was yesterday’s generation?") applies to all plants, first provide the **total combined generation**.  
#    - Then politely ask: *“Would you like me to show the breakdown by each plant as well?”*  
#    - Only show the breakdown if the customer requests it.  
#    - ⚡ **Important:** The same WhatsApp number may be linked to multiple plants. Each plant has a unique `plant_id`, which must be respected for accurate tool lookups. 
# 9. **Tone** – maintain a polite, professional style. Default to short answers unless detail is requested.  
# 10. **Greet the customer by `contact_person`** if available; otherwise, keep greeting generic.  
# ---
# """

#             self.app = create_react_agent(
#                 model=llm,
#                 tools=self.tools,
#                 checkpointer=None,
#                 prompt=system_prompt,
#             )
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
#             raise

#     async def ainvoke(self, user_text: str) -> str:
#         if self.app is None:
#             logger.error("[INVOKE] Client not connected, app is None")
#             raise RuntimeError("Client not connected. Call connect() first.")

#         thread_history = self.memory.get(self.thread_id, []) or []

#         messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in thread_history]
#         messages_for_state.append(("user", user_text))
#         state = {"messages": messages_for_state}
#         config = {"configurable": {"thread_id": self.thread_id}}

#         try:
#             result = await self.app.ainvoke(state, config=config)
#         except Exception as e:
#             logger.error(f"[INVOKE] Agent invocation failed: {e}")
#             raise

#         # Normalize result
#         content = "(no output)"
#         if isinstance(result, dict):
#             if "messages" in result and isinstance(result["messages"], list):
#                 last = result["messages"][-1]
#                 if isinstance(last, BaseMessage):
#                     content = last.content
#                 elif isinstance(last, tuple) and len(last) >= 2:
#                     content = last[1]
#                 elif isinstance(last, dict):
#                     content = last.get("content") or last.get("text")
#                 else:
#                     content = str(last)
#             elif "output" in result:
#                 content = str(result["output"])
#             elif "text" in result:
#                 content = str(result["text"])
#         elif isinstance(result, str):
#             content = result

#         # Persist history
#         now = datetime.now().astimezone().isoformat()
#         thread_history.append({"role": "user", "content": user_text, "timestamp": now})
#         thread_history.append({"role": "assistant", "content": content, "timestamp": now})

#         self.memory[self.thread_id] = thread_history
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()

#         return content

#     async def reset_history(self):
#         self.memory[self.thread_id] = []
#         self.save_memory()

#     async def close(self):
#         await self.exit_stack.aclose()
#         self.save_memory()

#     def _format_timestamp(self, iso_ts: Optional[str]) -> str:
#         if not iso_ts:
#             return ""
#         try:
#             dt = datetime.fromisoformat(iso_ts)
#             return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
#         except Exception as e:
#             logger.error(f"[HISTORY] Failed to format timestamp: {e}")
#             return iso_ts

#     def format_history(self, last_n: Optional[int] = None) -> str:
#         hist = self.memory.get(self.thread_id, []) or []
#         if last_n is not None:
#             hist = hist[-last_n:]
#         lines = []
#         for i, entry in enumerate(hist, start=1):
#             role = entry.get("role", "assistant").upper()
#             ts = self._format_timestamp(entry.get("timestamp", ""))
#             header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
#             body = entry.get("content", "")
#             lines.append(header)
#             lines.append(body)
#             lines.append("-" * 80)
#         return "\n".join(lines) if lines else "(no history)"
