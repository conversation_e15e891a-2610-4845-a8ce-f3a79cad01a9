<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Alarms Data Input</title>
  <style>
    .alarms-form-group {
      margin-bottom: 1rem;
      flex: 1 1 0;
      min-width: 0;
    }
    .alarms-form-label {
      font-weight: 500;
      margin-bottom: 0.3rem;
      display: block;
    }
    .alarms-form-input, .alarms-form-select {
      width: 100%;
      padding: 0.5rem;
      border-radius: 0.375rem;
      border: 1px solid #ced4da;
      font-size: 1rem;
      box-sizing: border-box;
    }
    .alarms-form-btn {
      background: #dc3545;
      color: #fff;
      border: none;
      border-radius: 0.375rem;
      padding: 0.5rem 1.2rem;
      font-weight: 600;
      cursor: pointer;
      margin-right: 0.5rem;
      transition: background 0.15s;
    }
    .alarms-form-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
    .alarms-btn-group {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }
    .alarms-btn-solar {
      background: #ffc107;
      color: #212529;
    }
    .alarms-btn-wind {
      background: #0dcaf0;
      color: #212529;
    }
    .alarms-btn-both {
      background: #198754;
      color: #fff;
    }
    .alarms-btn-active {
      outline: 2.5px solid #343a40;
      box-shadow: 0 0 0 2px #0dcaf0;
      z-index: 1;
    }
    .alarms-form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    .alarms-section-title {
      font-weight: 600;
      font-size: 1.1rem;
      color: #0d6efd;
      margin-bottom: 0.7rem;
      margin-top: 0.5rem;
      border-left: 4px solid #0dcaf0;
      padding-left: 0.7rem;
      background: #f8f9fa;
      border-radius: 0.3rem;
    }
    .alarms-section-box {
      background: #f8f9fa;
      border: 1.5px solid #e0e0e0;
      border-radius: 0.7rem;
      padding: 1rem 1.2rem;
      margin-bottom: 1.2rem;
    }
    @media (max-width: 600px) {
      .alarms-form-row {
        flex-direction: column;
        gap: 0;
      }
    }
  </style>
</head>
<body>
  <div class="alarms-btn-group">
    <button type="button" class="alarms-form-btn alarms-btn-solar" id="alarmsBtnSolar">Solar</button>
    <button type="button" class="alarms-form-btn alarms-btn-wind" id="alarmsBtnWind">Wind</button>
    <button type="button" class="alarms-form-btn alarms-btn-both alarms-btn-active" id="alarmsBtnBoth">Both</button>
  </div>

  <!-- Solar Form -->
  <form id="alarmsDataFormSolar" style="display:none;">
    <div class="alarms-section-title">Solar Plant Alarm</div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_plantname">Plant Name</label>
        <input class="alarms-form-input" type="text" id="solar_plantname" name="plantname" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_plantid">Plant ID</label>
        <input class="alarms-form-input" type="text" id="solar_plantid" name="plantid" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_alarmname">Alarm Name</label>
        <input class="alarms-form-input" type="text" id="solar_alarmname" name="alarmname" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_controllername">Controller Name</label>
        <input class="alarms-form-input" type="text" id="solar_controllername" name="controllername" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_message">Message</label>
        <input class="alarms-form-input" type="text" id="solar_message" name="message" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_severity">Severity</label>
        <select class="alarms-form-select" id="solar_severity" name="severity" required>
          <option value="">Select Severity</option>
          <option value="Critical">Critical</option>
          <option value="Major">Major</option>
          <option value="Minor">Minor</option>
          <option value="Warning">Warning</option>
          <option value="Info">Info</option>
        </select>
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_state">State</label>
        <select class="alarms-form-select" id="solar_state" name="state" required>
          <option value="">Select State</option>
          <option value="Active">Active</option>
          <option value="Inactive">Inactive</option>
          <option value="Acknowledged">Acknowledged</option>
          <option value="Cleared">Cleared</option>
        </select>
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_raised_time">Raised Time</label>
        <input class="alarms-form-input" type="datetime-local" id="solar_raised_time" name="raised_time" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_resolved_time">Resolved Time</label>
        <input class="alarms-form-input" type="datetime-local" id="solar_resolved_time" name="resolved_time" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="solar_duration">Duration</label>
        <input class="alarms-form-input" type="text" id="solar_duration" name="duration" readonly />
      </div>
    </div>
    <div style="text-align:right;">
      <button type="submit" class="alarms-form-btn">Submit</button>
      <button type="button" class="alarms-form-btn" style="background:#6c757d;" onclick="window.closeModal && window.closeModal()">Cancel</button>
    </div>
  </form>

  <!-- Wind Form -->
  <form id="alarmsDataFormWind" style="display:none;">
    <div class="alarms-section-title">Wind Plant Alarm</div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_plantname">Plant Name</label>
        <input class="alarms-form-input" type="text" id="wind_plantname" name="plantname" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_plantid">Plant ID</label>
        <input class="alarms-form-input" type="text" id="wind_plantid" name="plantid" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_alarmname">Alarm Name</label>
        <input class="alarms-form-input" type="text" id="wind_alarmname" name="alarmname" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_controllername">Controller Name</label>
        <input class="alarms-form-input" type="text" id="wind_controllername" name="controllername" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_message">Message</label>
        <input class="alarms-form-input" type="text" id="wind_message" name="message" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_severity">Severity</label>
        <select class="alarms-form-select" id="wind_severity" name="severity" required>
          <option value="">Select Severity</option>
          <option value="Critical">Critical</option>
          <option value="Major">Major</option>
          <option value="Minor">Minor</option>
          <option value="Warning">Warning</option>
          <option value="Info">Info</option>
        </select>
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_state">State</label>
        <select class="alarms-form-select" id="wind_state" name="state" required>
          <option value="">Select State</option>
          <option value="Active">Active</option>
          <option value="Inactive">Inactive</option>
          <option value="Acknowledged">Acknowledged</option>
          <option value="Cleared">Cleared</option>
        </select>
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_raised_time">Raised Time</label>
        <input class="alarms-form-input" type="datetime-local" id="wind_raised_time" name="raised_time" required />
      </div>
    </div>
    <div class="alarms-form-row">
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_resolved_time">Resolved Time</label>
        <input class="alarms-form-input" type="datetime-local" id="wind_resolved_time" name="resolved_time" required />
      </div>
      <div class="alarms-form-group">
        <label class="alarms-form-label" for="wind_duration">Duration</label>
        <input class="alarms-form-input" type="text" id="wind_duration" name="duration" readonly />
      </div>
    </div>
    <div style="text-align:right;">
      <button type="submit" class="alarms-form-btn">Submit</button>
      <button type="button" class="alarms-form-btn" style="background:#6c757d;" onclick="window.closeModal && window.closeModal()">Cancel</button>
    </div>
  </form>

  <!-- Both (Hybrid) Form -->
  <form id="alarmsDataFormBoth">
    <div class="alarms-section-title">Hybrid Plant Alarm</div>
    <div class="alarms-section-box">
      <div class="alarms-section-title" style="font-size:1rem;">Solar Section</div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_plantname">Plant Name</label>
          <input class="alarms-form-input" type="text" id="both_solar_plantname" name="solar_plantname" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_plantid">Plant ID</label>
          <input class="alarms-form-input" type="text" id="both_solar_plantid" name="solar_plantid" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_alarmname">Alarm Name</label>
          <input class="alarms-form-input" type="text" id="both_solar_alarmname" name="solar_alarmname" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_controllername">Controller Name</label>
          <input class="alarms-form-input" type="text" id="both_solar_controllername" name="solar_controllername" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_message">Message</label>
          <input class="alarms-form-input" type="text" id="both_solar_message" name="solar_message" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_severity">Severity</label>
          <select class="alarms-form-select" id="both_solar_severity" name="solar_severity" required>
            <option value="">Select Severity</option>
            <option value="Critical">Critical</option>
            <option value="Major">Major</option>
            <option value="Minor">Minor</option>
            <option value="Warning">Warning</option>
            <option value="Info">Info</option>
          </select>
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_state">State</label>
          <select class="alarms-form-select" id="both_solar_state" name="solar_state" required>
            <option value="">Select State</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Acknowledged">Acknowledged</option>
            <option value="Cleared">Cleared</option>
          </select>
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_raised_time">Raised Time</label>
          <input class="alarms-form-input" type="datetime-local" id="both_solar_raised_time" name="solar_raised_time" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_resolved_time">Resolved Time</label>
          <input class="alarms-form-input" type="datetime-local" id="both_solar_resolved_time" name="solar_resolved_time" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_solar_duration">Duration</label>
          <input class="alarms-form-input" type="text" id="both_solar_duration" name="solar_duration" readonly />
        </div>
      </div>
    </div>
    <div class="alarms-section-box">
      <div class="alarms-section-title" style="font-size:1rem;">Wind Section</div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_plantname">Plant Name</label>
          <input class="alarms-form-input" type="text" id="both_wind_plantname" name="wind_plantname" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_plantid">Plant ID</label>
          <input class="alarms-form-input" type="text" id="both_wind_plantid" name="wind_plantid" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_alarmname">Alarm Name</label>
          <input class="alarms-form-input" type="text" id="both_wind_alarmname" name="wind_alarmname" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_controllername">Controller Name</label>
          <input class="alarms-form-input" type="text" id="both_wind_controllername" name="wind_controllername" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_message">Message</label>
          <input class="alarms-form-input" type="text" id="both_wind_message" name="wind_message" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_severity">Severity</label>
          <select class="alarms-form-select" id="both_wind_severity" name="wind_severity" required>
            <option value="">Select Severity</option>
            <option value="Critical">Critical</option>
            <option value="Major">Major</option>
            <option value="Minor">Minor</option>
            <option value="Warning">Warning</option>
            <option value="Info">Info</option>
          </select>
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_state">State</label>
          <select class="alarms-form-select" id="both_wind_state" name="wind_state" required>
            <option value="">Select State</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Acknowledged">Acknowledged</option>
            <option value="Cleared">Cleared</option>
          </select>
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_raised_time">Raised Time</label>
          <input class="alarms-form-input" type="datetime-local" id="both_wind_raised_time" name="wind_raised_time" required />
        </div>
      </div>
      <div class="alarms-form-row">
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_resolved_time">Resolved Time</label>
          <input class="alarms-form-input" type="datetime-local" id="both_wind_resolved_time" name="wind_resolved_time" required />
        </div>
        <div class="alarms-form-group">
          <label class="alarms-form-label" for="both_wind_duration">Duration</label>
          <input class="alarms-form-input" type="text" id="both_wind_duration" name="wind_duration" readonly />
        </div>
      </div>
    </div>
    <div style="text-align:right;">
      <button type="submit" class="alarms-form-btn">Submit</button>
      <button type="button" class="alarms-form-btn" style="background:#6c757d;" onclick="window.closeModal && window.closeModal()">Cancel</button>
    </div>
  </form>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Helper to show only the selected form
      function showAlarmsForm(type) {
        document.getElementById('alarmsDataFormSolar').style.display = (type === 'solar') ? '' : 'none';
        document.getElementById('alarmsDataFormWind').style.display = (type === 'wind') ? '' : 'none';
        document.getElementById('alarmsDataFormBoth').style.display = (type === 'both') ? '' : 'none';

        // Update button active state
        document.getElementById('alarmsBtnSolar').classList.toggle('alarms-btn-active', type === 'solar');
        document.getElementById('alarmsBtnWind').classList.toggle('alarms-btn-active', type === 'wind');
        document.getElementById('alarmsBtnBoth').classList.toggle('alarms-btn-active', type === 'both');
      }

      // Button click handlers
      document.getElementById('alarmsBtnSolar').onclick = function() { showAlarmsForm('solar'); };
      document.getElementById('alarmsBtnWind').onclick = function() { showAlarmsForm('wind'); };
      document.getElementById('alarmsBtnBoth').onclick = function() { showAlarmsForm('both'); };

      // Default: hide all forms
      showAlarmsForm('');

      // Duration calculation helper
      function calculateDuration(start, end) {
        if (!start || !end) return '';
        const startDate = new Date(start);
        const endDate = new Date(end);
        if (isNaN(startDate) || isNaN(endDate) || endDate < startDate) return '';
        let diffMs = endDate - startDate;
        const diffSec = Math.floor(diffMs / 1000) % 60;
        const diffMin = Math.floor(diffMs / (1000 * 60)) % 60;
        const diffHr = Math.floor(diffMs / (1000 * 60 * 60)) % 24;
        const diffDay = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        let result = '';
        if (diffDay > 0) result += diffDay + 'd ';
        if (diffHr > 0 || diffDay > 0) result += diffHr + 'h ';
        if (diffMin > 0 || diffHr > 0 || diffDay > 0) result += diffMin + 'm ';
        result += diffSec + 's';
        return result.trim();
      }

      // Solar duration auto-calc
      function updateSolarDuration() {
        const raised = document.getElementById('solar_raised_time').value;
        const resolved = document.getElementById('solar_resolved_time').value;
        document.getElementById('solar_duration').value = calculateDuration(raised, resolved);
      }
      document.getElementById('solar_raised_time').addEventListener('change', updateSolarDuration);
      document.getElementById('solar_resolved_time').addEventListener('change', updateSolarDuration);

      // Wind duration auto-calc
      function updateWindDuration() {
        const raised = document.getElementById('wind_raised_time').value;
        const resolved = document.getElementById('wind_resolved_time').value;
        document.getElementById('wind_duration').value = calculateDuration(raised, resolved);
      }
      document.getElementById('wind_raised_time').addEventListener('change', updateWindDuration);
      document.getElementById('wind_resolved_time').addEventListener('change', updateWindDuration);

      // Both Solar duration auto-calc
      function updateBothSolarDuration() {
        const raised = document.getElementById('both_solar_raised_time').value;
        const resolved = document.getElementById('both_solar_resolved_time').value;
        document.getElementById('both_solar_duration').value = calculateDuration(raised, resolved);
      }
      document.getElementById('both_solar_raised_time').addEventListener('change', updateBothSolarDuration);
      document.getElementById('both_solar_resolved_time').addEventListener('change', updateBothSolarDuration);

      // Both Wind duration auto-calc
      function updateBothWindDuration() {
        const raised = document.getElementById('both_wind_raised_time').value;
        const resolved = document.getElementById('both_wind_resolved_time').value;
        document.getElementById('both_wind_duration').value = calculateDuration(raised, resolved);
      }
      document.getElementById('both_wind_raised_time').addEventListener('change', updateBothWindDuration);
      document.getElementById('both_wind_resolved_time').addEventListener('change', updateBothWindDuration);

    })();
  </script>
</body>
</html>
